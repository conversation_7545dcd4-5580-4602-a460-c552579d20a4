const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入配置
const config = require('./config/config');

// 验证配置
config.validate();

const app = express();
const PORT = config.server.port;

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 基础中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 设置视图引擎
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'html');

// 自定义HTML模板引擎
app.engine('html', (filePath, options, callback) => {
    const fs = require('fs');
    fs.readFile(filePath, (err, content) => {
        if (err) return callback(err);
        
        // 简单的模板替换
        let rendered = content.toString();
        for (let key in options) {
            if (key !== 'settings' && key !== 'cache' && key !== '_locals') {
                const regex = new RegExp(`{{${key}}}`, 'g');
                rendered = rendered.replace(regex, options[key]);
            }
        }
        return callback(null, rendered);
    });
});

// 路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'index.html'));
});

// API路由
app.use('/api', require('./server/routes/api'));

// 404处理
app.use((req, res) => {
    res.status(404).sendFile(path.join(__dirname, 'views', '404.html'));
});

// 错误处理
app.use((err, req, res, next) => {
    console.error('服务器错误:', err.stack);
    res.status(500).json({ 
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? err.message : '请稍后重试'
    });
});

// 初始化服务
async function initializeServices() {
    try {
        // 初始化数据库
        const database = require('./server/models/Database');
        await database.init();

        // 初始化邮件服务
        const EmailService = require('./server/services/EmailService');
        const emailService = new EmailService();
        await emailService.init();

        // 将服务实例添加到app中，供路由使用
        app.locals.emailService = emailService;

        console.log('所有服务初始化完成');
    } catch (error) {
        console.error('服务初始化失败:', error);
        process.exit(1);
    }
}

// 启动服务器
async function startServer() {
    try {
        await initializeServices();

        app.listen(PORT, () => {
            console.log(`临时邮箱管理系统运行在 http://localhost:${PORT}`);
            console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
        });
    } catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n正在关闭服务器...');

    if (app.locals.emailService) {
        await app.locals.emailService.stop();
    }

    const database = require('./server/models/Database');
    if (database.isConnected()) {
        await database.close();
    }

    console.log('服务器已关闭');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');

    if (app.locals.emailService) {
        await app.locals.emailService.stop();
    }

    const database = require('./server/models/Database');
    if (database.isConnected()) {
        await database.close();
    }

    process.exit(0);
});

// 启动应用
startServer();

module.exports = app;
