/**
 * 临时邮箱管理系统 - 前端应用
 * 完全模仿 getmailapp.org 的交互体验
 */

class TempEmailApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'home';
        this.emails = [];
        this.currentEmailPage = 1;
        this.emailsPerPage = 20;
        this.refreshInterval = null;
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.bindEvents();
        this.checkAuthStatus();
        this.initTheme();
        
        // 如果已登录，直接显示邮箱页面
        const token = localStorage.getItem('authToken');
        if (token) {
            this.showPage('mailbox');
            this.loadEmails();
            this.startEmailRefresh();
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主题切换
        document.getElementById('themeBtn').addEventListener('click', () => {
            this.toggleTheme();
        });

        // 菜单按钮
        document.getElementById('menuBtn').addEventListener('click', () => {
            this.toggleMenu();
        });

        // 开始使用按钮
        document.getElementById('startBtn').addEventListener('click', () => {
            this.showPage('login');
        });

        // 登录表单
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            this.handleLogin(e);
        });

        // 密码显示/隐藏
        document.getElementById('passwordToggle').addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // 复制邮箱地址
        document.getElementById('copyEmailBtn').addEventListener('click', () => {
            this.copyEmailAddress();
        });

        // 刷新邮件
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadEmails();
        });

        // 分页按钮
        document.getElementById('prevPage').addEventListener('click', () => {
            this.changePage(-1);
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            this.changePage(1);
        });

        // 注销按钮
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // 菜单遮罩点击关闭
        document.getElementById('menuOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.toggleMenu();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * 检查认证状态
     */
    checkAuthStatus() {
        const token = localStorage.getItem('authToken');
        const email = localStorage.getItem('userEmail');
        
        if (token && email) {
            this.currentUser = { email, token };
            this.updateUserInterface();
        }
    }

    /**
     * 初始化主题
     */
    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        this.showNotification('主题已切换', 'success');
    }

    /**
     * 切换菜单显示
     */
    toggleMenu() {
        const menuOverlay = document.getElementById('menuOverlay');
        menuOverlay.classList.toggle('active');
    }

    /**
     * 显示指定页面
     */
    showPage(pageName) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示指定页面
        document.getElementById(`${pageName}Page`).classList.add('active');
        this.currentPage = pageName;
    }

    /**
     * 处理登录
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username) {
            this.showNotification('请输入用户名', 'error');
            return;
        }

        // 构造邮箱地址
        const email = `${username}@getmailapp.org`;
        
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (data.success) {
                // 保存认证信息
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userEmail', data.email);
                
                this.currentUser = { email: data.email, token: data.token };
                this.updateUserInterface();
                
                // 切换到邮箱页面
                this.showPage('mailbox');
                this.loadEmails();
                this.startEmailRefresh();
                
                this.showNotification('登录成功', 'success');
            } else {
                this.showNotification(data.error || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showNotification('网络错误，请稍后重试', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const isPassword = passwordInput.type === 'password';
        
        passwordInput.type = isPassword ? 'text' : 'password';
        
        // 更新图标（这里可以添加图标切换逻辑）
    }

    /**
     * 更新用户界面
     */
    updateUserInterface() {
        if (this.currentUser) {
            const username = this.currentUser.email.split('@')[0];
            document.getElementById('currentEmail').textContent = this.currentUser.email;
            document.getElementById('menuUsername').textContent = username;
        }
    }

    /**
     * 复制邮箱地址
     */
    async copyEmailAddress() {
        if (!this.currentUser) return;
        
        try {
            await navigator.clipboard.writeText(this.currentUser.email);
            this.showNotification('邮箱地址已复制', 'success');
        } catch (error) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = this.currentUser.email;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            this.showNotification('邮箱地址已复制', 'success');
        }
    }

    /**
     * 加载邮件列表
     */
    async loadEmails() {
        if (!this.currentUser) return;
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/emails?page=${this.currentEmailPage}&limit=${this.emailsPerPage}`, {
                headers: {
                    'Authorization': `Bearer ${this.currentUser.token}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.emails = data.emails;
                this.renderEmailList();
                this.updatePagination(data.pagination);
                this.updateLastRefreshTime();
            } else {
                this.showNotification(data.error || '加载邮件失败', 'error');
            }
        } catch (error) {
            console.error('加载邮件错误:', error);
            this.showNotification('网络错误，请稍后重试', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染邮件列表
     */
    renderEmailList() {
        const mailItems = document.getElementById('mailItems');
        const emptyState = document.getElementById('emptyState');

        if (this.emails.length === 0) {
            mailItems.innerHTML = '';
            emptyState.style.display = 'flex';
            return;
        }

        emptyState.style.display = 'none';

        mailItems.innerHTML = this.emails.map(email => `
            <div class="mail-item ${email.read ? '' : 'unread'}" data-email-id="${email.id}">
                <div class="mail-content">
                    <div class="mail-subject">${this.escapeHtml(email.subject || '(无主题)')}</div>
                    <div class="mail-preview">${this.escapeHtml(email.preview || email.text || '')}</div>
                </div>
                <div class="mail-time">${this.formatTime(email.date)}</div>
            </div>
        `).join('');

        // 绑定邮件点击事件
        mailItems.querySelectorAll('.mail-item').forEach(item => {
            item.addEventListener('click', () => {
                const emailId = item.dataset.emailId;
                this.viewEmailDetail(emailId);
            });
        });
    }

    /**
     * 更新分页信息
     */
    updatePagination(pagination) {
        const pageInfo = document.getElementById('pageInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        pageInfo.textContent = `${pagination.page} / ${pagination.totalPages}`;

        prevBtn.disabled = pagination.page <= 1;
        nextBtn.disabled = pagination.page >= pagination.totalPages;
    }

    /**
     * 切换页面
     */
    changePage(direction) {
        this.currentEmailPage += direction;
        this.loadEmails();
    }

    /**
     * 更新最后刷新时间
     */
    updateLastRefreshTime() {
        const statusText = document.querySelector('.status-text');
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        statusText.textContent = `上次刷新：${timeString}`;
    }

    /**
     * 开始邮件自动刷新
     */
    startEmailRefresh() {
        // 清除现有定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // 设置新的定时器（每30秒刷新一次）
        this.refreshInterval = setInterval(() => {
            this.loadEmails();
        }, 30000);
    }

    /**
     * 停止邮件自动刷新
     */
    stopEmailRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 注销
     */
    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userEmail');

        this.currentUser = null;
        this.emails = [];
        this.stopEmailRefresh();

        this.showPage('home');
        this.toggleMenu();

        this.showNotification('已注销', 'success');
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (show) {
            loadingOverlay.classList.add('active');
        } else {
            loadingOverlay.classList.remove('active');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notificationText');

        notificationText.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // ESC键关闭模态框和菜单
        if (e.key === 'Escape') {
            this.closeEmailModal();
            const menuOverlay = document.getElementById('menuOverlay');
            if (menuOverlay.classList.contains('active')) {
                this.toggleMenu();
            }
        }

        // Ctrl/Cmd + R 刷新邮件
        if ((e.ctrlKey || e.metaKey) && e.key === 'r' && this.currentPage === 'mailbox') {
            e.preventDefault();
            this.loadEmails();
        }
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 格式化时间
     */
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return date.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }

    /**
     * 格式化完整时间
     */
    formatFullTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    /**
     * 格式化纯文本
     */
    formatPlainText(text) {
        return text.replace(/\n/g, '<br>');
    }

    /**
     * 查看邮件详情
     */
    async viewEmailDetail(emailId) {
        if (!this.currentUser) return;

        this.showLoading(true);

        try {
            const response = await fetch(`/api/emails/${emailId}`, {
                headers: {
                    'Authorization': `Bearer ${this.currentUser.token}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showEmailModal(data.email);

                // 标记为已读
                if (!data.email.read) {
                    this.markEmailAsRead(emailId);
                }
            } else {
                this.showNotification(data.error || '加载邮件详情失败', 'error');
            }
        } catch (error) {
            console.error('加载邮件详情错误:', error);
            this.showNotification('网络错误，请稍后重试', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 标记邮件为已读
     */
    async markEmailAsRead(emailId) {
        if (!this.currentUser) return;

        try {
            const response = await fetch(`/api/emails/${emailId}/read`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.currentUser.token}`
                }
            });

            const data = await response.json();

            if (data.success) {
                // 更新本地邮件状态
                const emailElement = document.querySelector(`[data-email-id="${emailId}"]`);
                if (emailElement) {
                    emailElement.classList.remove('unread');
                }
            }
        } catch (error) {
            console.error('标记邮件已读失败:', error);
        }
    }

    /**
     * 显示邮件详情模态框
     */
    showEmailModal(email) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="email-modal-overlay" id="emailModal">
                <div class="email-modal">
                    <div class="email-modal-header">
                        <h3 class="email-modal-title">${this.escapeHtml(email.subject || '(无主题)')}</h3>
                        <button class="email-modal-close" id="closeEmailModal">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                    <div class="email-modal-meta">
                        <div class="email-meta-item">
                            <strong>发件人:</strong> ${this.escapeHtml(email.from || '未知')}
                        </div>
                        <div class="email-meta-item">
                            <strong>收件人:</strong> ${this.escapeHtml(email.to || '')}
                        </div>
                        <div class="email-meta-item">
                            <strong>时间:</strong> ${this.formatFullTime(email.date)}
                        </div>
                    </div>
                    <div class="email-modal-content">
                        ${email.html || this.formatPlainText(email.text || '邮件内容为空')}
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定关闭事件
        const modal = document.getElementById('emailModal');
        const closeBtn = document.getElementById('closeEmailModal');

        closeBtn.addEventListener('click', () => {
            this.closeEmailModal();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeEmailModal();
            }
        });

        // 添加样式
        this.addEmailModalStyles();
    }

    /**
     * 关闭邮件详情模态框
     */
    closeEmailModal() {
        const modal = document.getElementById('emailModal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 添加邮件模态框样式
     */
    addEmailModalStyles() {
        if (document.getElementById('emailModalStyles')) return;

        const styles = `
            <style id="emailModalStyles">
                .email-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: rgba(0, 0, 0, 0.7);
                    z-index: 5000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: var(--spacing-lg);
                }

                .email-modal {
                    background-color: var(--background-color);
                    border-radius: var(--radius-xl);
                    max-width: 800px;
                    width: 100%;
                    max-height: 90vh;
                    overflow: hidden;
                    box-shadow: var(--shadow-lg);
                    display: flex;
                    flex-direction: column;
                }

                .email-modal-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: var(--spacing-lg);
                    border-bottom: 1px solid var(--border-color);
                    background-color: var(--surface-color);
                }

                .email-modal-title {
                    margin: 0;
                    color: var(--text-primary);
                    font-size: var(--font-size-lg);
                }

                .email-modal-close {
                    background: none;
                    border: none;
                    color: var(--text-secondary);
                    cursor: pointer;
                    padding: var(--spacing-xs);
                    border-radius: var(--radius-sm);
                    transition: all 0.2s ease;
                }

                .email-modal-close:hover {
                    background-color: var(--border-color);
                    color: var(--text-primary);
                }

                .email-modal-meta {
                    padding: var(--spacing-lg);
                    border-bottom: 1px solid var(--border-color);
                    background-color: var(--surface-color);
                }

                .email-meta-item {
                    margin-bottom: var(--spacing-sm);
                    color: var(--text-secondary);
                    font-size: var(--font-size-sm);
                }

                .email-modal-content {
                    padding: var(--spacing-lg);
                    overflow-y: auto;
                    flex: 1;
                    line-height: 1.6;
                }

                @media (max-width: 768px) {
                    .email-modal-overlay {
                        padding: var(--spacing-md);
                    }

                    .email-modal {
                        max-height: 95vh;
                    }

                    .email-modal-header,
                    .email-modal-meta,
                    .email-modal-content {
                        padding: var(--spacing-md);
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.tempEmailApp = new TempEmailApp();
});
