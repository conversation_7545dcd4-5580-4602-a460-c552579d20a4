/* 基础样式重置和变量定义 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色变量 */
    --primary-color: #000000;
    --secondary-color: #666666;
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --border-color: #e1e5e9;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-muted: #999999;
    --accent-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    
    /* 间距变量 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 圆角变量 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* 阴影变量 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    
    /* 字体变量 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
}

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #ffffff;
    --secondary-color: #cccccc;
    --background-color: #1a1a1a;
    --surface-color: #2d2d2d;
    --border-color: #404040;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-size-md);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 顶部导航栏样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.logo-icon {
    font-size: var(--font-size-xl);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background-color: var(--surface-color);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
    padding: var(--spacing-lg);
}

/* 页面切换 */
.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

/* 首页英雄区域 */
.hero-section {
    text-align: center;
    padding: var(--spacing-xxl) 0;
}

.hero-title {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xxl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 功能特性网格 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.feature-card {
    background-color: var(--surface-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.feature-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-desc {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* CTA按钮 */
.cta-button {
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    opacity: 0.9;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    max-width: 400px;
    margin: 0 auto;
    padding: var(--spacing-xxl);
}

.login-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
}

.login-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xxl);
    color: var(--text-primary);
    text-align: center;
}

.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-md);
    background-color: var(--background-color);
    color: var(--text-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.login-button {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: var(--spacing-md);
}

.login-button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 邮箱管理页面样式 */
.mailbox-container {
    max-width: 1000px;
    margin: 0 auto;
}

.mailbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--surface-color);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.mailbox-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.mailbox-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.mailbox-address {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background-color: var(--background-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.copy-btn, .action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.copy-btn:hover, .action-btn:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.mailbox-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.mail-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.status-icon {
    font-size: var(--font-size-md);
}

/* 邮件列表样式 */
.mail-list {
    background-color: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.mail-list-header {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.mail-items {
    min-height: 400px;
}

.mail-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mail-item:hover {
    background-color: var(--background-color);
}

.mail-item:last-child {
    border-bottom: none;
}

.mail-item.unread {
    background-color: rgba(0, 123, 255, 0.05);
}

.mail-subject {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.mail-preview {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.mail-time {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    white-space: nowrap;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
}

.empty-text {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.page-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover:not(:disabled) {
    background-color: var(--surface-color);
    color: var(--text-primary);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    min-width: 60px;
    text-align: center;
}

/* 菜单弹窗样式 */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 80px var(--spacing-lg) var(--spacing-lg);
}

.menu-overlay.active {
    display: flex;
}

.menu-popup {
    background-color: var(--background-color);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    overflow: hidden;
    animation: menuSlideIn 0.3s ease;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--surface-color);
}

.menu-username {
    font-weight: 600;
    color: var(--text-primary);
}

.menu-items {
    padding: var(--spacing-sm);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;
}

.menu-item:hover {
    background-color: var(--surface-color);
}

.menu-icon {
    font-size: var(--font-size-md);
    width: 20px;
    text-align: center;
}

/* 加载和通知样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: var(--spacing-md);
    color: var(--text-secondary);
}

.notification {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--background-color);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 4000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background-color: var(--success-color);
}

.notification.error {
    background-color: var(--error-color);
}

.notification.warning {
    background-color: var(--warning-color);
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: var(--spacing-md);
    }

    .logo-text {
        display: none;
    }

    .main-content {
        padding: var(--spacing-md);
        margin-top: 70px;
    }

    .hero-title {
        font-size: var(--font-size-xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-md);
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    .login-container {
        padding: var(--spacing-lg);
    }

    .mailbox-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .mailbox-info {
        justify-content: center;
    }

    .mailbox-actions {
        justify-content: center;
    }

    .mail-list-header {
        padding: var(--spacing-md);
    }

    .mail-item {
        padding: var(--spacing-md);
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .mail-time {
        font-size: var(--font-size-xs);
    }

    .menu-overlay {
        padding: 70px var(--spacing-md) var(--spacing-md);
    }

    .notification {
        right: var(--spacing-md);
        left: var(--spacing-md);
        top: 80px;
    }
}

@media (max-width: 480px) {
    .mailbox-address {
        font-size: var(--font-size-xs);
        word-break: break-all;
    }

    .mail-subject {
        font-size: var(--font-size-sm);
    }

    .mail-preview {
        font-size: var(--font-size-xs);
        -webkit-line-clamp: 1;
    }

    .feature-card {
        padding: var(--spacing-md);
    }

    .hero-section {
        padding: var(--spacing-lg) 0;
    }
}

/* 打印样式 */
@media print {
    .header,
    .mailbox-actions,
    .pagination,
    .menu-overlay,
    .loading-overlay,
    .notification {
        display: none !important;
    }

    .main-content {
        margin-top: 0;
    }

    .mail-item {
        break-inside: avoid;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
