<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 无限临时邮箱</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
    <style>
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: var(--spacing-lg);
        }
        
        .error-icon {
            font-size: 120px;
            margin-bottom: var(--spacing-lg);
        }
        
        .error-title {
            font-size: var(--font-size-xxl);
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }
        
        .error-message {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
            max-width: 500px;
        }
        
        .error-actions {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .error-btn {
            background-color: var(--primary-color);
            color: var(--background-color);
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            font-size: var(--font-size-md);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .error-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .error-btn.secondary {
            background-color: var(--surface-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        @media (max-width: 768px) {
            .error-icon {
                font-size: 80px;
            }
            
            .error-title {
                font-size: var(--font-size-xl);
            }
            
            .error-message {
                font-size: var(--font-size-md);
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-btn {
                width: 200px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">📭</div>
        <h1 class="error-title">页面未找到</h1>
        <p class="error-message">
            抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
        </p>
        <div class="error-actions">
            <a href="/" class="error-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                返回首页
            </a>
            <button onclick="history.back()" class="error-btn secondary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m12 19-7-7 7-7"/>
                    <path d="M19 12H5"/>
                </svg>
                返回上页
            </button>
        </div>
    </div>

    <script>
        // 自动应用主题
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // 3秒后自动跳转到首页
        setTimeout(() => {
            if (confirm('页面未找到，是否返回首页？')) {
                window.location.href = '/';
            }
        }, 3000);
    </script>
</body>
</html>
