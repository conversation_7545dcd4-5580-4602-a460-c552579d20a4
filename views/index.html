<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📧 无限临时邮箱</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span class="logo-icon">📧</span>
                <span class="logo-text">无限临时邮箱</span>
            </div>
            <div class="header-actions">
                <button class="icon-btn" id="languageBtn" title="语言">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="2" y1="12" x2="22" y2="12"/>
                        <path d="m8 12 4-4 4 4"/>
                    </svg>
                </button>
                <button class="icon-btn" id="themeBtn" title="主题">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"/>
                        <line x1="12" y1="1" x2="12" y2="3"/>
                        <line x1="12" y1="21" x2="12" y2="23"/>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                        <line x1="1" y1="12" x2="3" y2="12"/>
                        <line x1="21" y1="12" x2="23" y2="12"/>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                    </svg>
                </button>
                <button class="icon-btn" id="menuBtn" title="菜单">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="1"/>
                        <circle cx="12" cy="5" r="1"/>
                        <circle cx="12" cy="19" r="1"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 未登录状态的首页 -->
        <div id="homePage" class="page active">
            <div class="hero-section">
                <h1 class="hero-title">无限临时邮箱</h1>
                <p class="hero-subtitle">免费、安全、实时，无限制临时邮箱服务，为您的隐私保驾护航</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3 class="feature-title">隐私保护</h3>
                        <p class="feature-desc">无需注册个人信息，所有邮件数据加密存储，确保您的隐私安全不被泄露。</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📧</div>
                        <h3 class="feature-title">无限数量邮箱地址</h3>
                        <p class="feature-desc">注册用户名后可自动生成邮箱地址，邮件会自动到达您的地址</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⏱️</div>
                        <h3 class="feature-title">实时收件</h3>
                        <p class="feature-desc">即时接收邮件通知，支持附件下载，让您不错过任何重要信息。</p>
                    </div>
                </div>

                <button class="cta-button" id="startBtn">
                    <span>开始使用</span>
                </button>
            </div>
        </div>

        <!-- 登录页面 -->
        <div id="loginPage" class="page">
            <div class="login-container">
                <div class="login-icon">📧</div>
                <h2 class="login-title">还有没有配好邮箱</h2>
                
                <form class="login-form" id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" placeholder="luosiyuan" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <button type="submit" class="login-button">登录</button>
                </form>
            </div>
        </div>

        <!-- 邮箱管理页面 -->
        <div id="mailboxPage" class="page">
            <div class="mailbox-container">
                <!-- 邮箱信息栏 -->
                <div class="mailbox-header">
                    <div class="mailbox-info">
                        <span class="mailbox-label">你的邮箱：</span>
                        <span class="mailbox-address" id="currentEmail"><EMAIL></span>
                        <button class="copy-btn" id="copyEmailBtn" title="复制邮箱地址">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                            </svg>
                        </button>
                    </div>
                    <div class="mailbox-actions">
                        <button class="action-btn" id="refreshBtn" title="刷新">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23 4 23 10 17 10"/>
                                <polyline points="1 20 1 14 7 14"/>
                                <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
                            </svg>
                        </button>
                        <button class="action-btn" id="deleteBtn" title="删除">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"/>
                                <path d="m19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 邮件状态信息 -->
                <div class="mail-status">
                    <span class="status-icon">⏰</span>
                    <span class="status-text">上次刷新：9:14:40</span>
                </div>

                <!-- 邮件列表 -->
                <div class="mail-list">
                    <div class="mail-list-header">
                        <div class="mail-header-subject">邮件</div>
                        <div class="mail-header-time">时间</div>
                    </div>
                    
                    <div class="mail-items" id="mailItems">
                        <!-- 邮件项目将通过JavaScript动态加载 -->
                    </div>
                    
                    <!-- 空状态 -->
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">📭</div>
                        <p class="empty-text">请选择一封邮件查看内容</p>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <button class="page-btn" id="prevPage">‹</button>
                    <span class="page-info" id="pageInfo">1</span>
                    <button class="page-btn" id="nextPage">›</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 用户菜单弹窗 -->
    <div class="menu-overlay" id="menuOverlay">
        <div class="menu-popup">
            <div class="menu-header">
                <span class="menu-username" id="menuUsername">luosiyuan</span>
            </div>
            <div class="menu-items">
                <a href="#" class="menu-item">
                    <span class="menu-icon">🧭</span>
                    <span>导航</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="menu-icon">🎨</span>
                    <span>主题</span>
                </a>
                <a href="#" class="menu-item">
                    <span class="menu-icon">📍</span>
                    <span>联系</span>
                </a>
                <a href="#" class="menu-item" id="logoutBtn">
                    <span class="menu-icon">↗️</span>
                    <span>注销</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 加载中提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p class="loading-text">加载中...</p>
    </div>

    <!-- 通知提示 -->
    <div class="notification" id="notification">
        <span class="notification-text" id="notificationText"></span>
    </div>

    <script src="/js/app.js"></script>
</body>
</html>
