/**
 * 应用配置文件
 * 集中管理所有配置项
 */

const config = {
    // 服务器配置
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || 'localhost',
        env: process.env.NODE_ENV || 'development'
    },

    // 数据库配置
    database: {
        path: process.env.DB_PATH || './database/emails.db',
        options: {
            // SQLite 配置选项
            timeout: 5000,
            verbose: process.env.NODE_ENV === 'development' ? console.log : null
        }
    },

    // 邮件服务配置
    email: {
        imap: {
            host: process.env.IMAP_HOST || 'imap.gmail.com',
            port: parseInt(process.env.IMAP_PORT) || 993,
            user: process.env.IMAP_USER,
            password: process.env.IMAP_PASSWORD,
            tls: process.env.IMAP_TLS === 'true',
            tlsOptions: {
                rejectUnauthorized: false
            },
            connTimeout: 60000, // 连接超时
            authTimeout: 5000,  // 认证超时
            keepalive: true
        },
        
        // 邮件检查配置
        checkInterval: parseInt(process.env.EMAIL_CHECK_INTERVAL) || 30000, // 30秒
        retentionHours: parseInt(process.env.EMAIL_RETENTION_HOURS) || 24,  // 24小时
        maxEmailsPerUser: parseInt(process.env.MAX_EMAILS_PER_USER) || 100,  // 每用户最大邮件数
        
        // 支持的域名
        allowedDomains: [
            process.env.DOMAIN || 'getmailapp.org'
        ]
    },

    // JWT配置
    jwt: {
        secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
        expiresIn: '24h',
        algorithm: 'HS256'
    },

    // 安全配置
    security: {
        // 速率限制
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15分钟
            max: 100, // 每个IP最多100个请求
            message: '请求过于频繁，请稍后再试'
        },
        
        // CORS配置
        cors: {
            origin: process.env.CORS_ORIGIN || '*',
            credentials: true,
            optionsSuccessStatus: 200
        },
        
        // Helmet安全头配置
        helmet: {
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'"],
                    fontSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    mediaSrc: ["'self'"],
                    frameSrc: ["'none'"]
                }
            },
            crossOriginEmbedderPolicy: false
        }
    },

    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined',
        file: process.env.LOG_FILE || './logs/app.log'
    },

    // 应用配置
    app: {
        name: 'Temp Email Manager',
        version: '1.0.0',
        description: '临时邮箱管理系统',
        
        // 分页配置
        pagination: {
            defaultLimit: 20,
            maxLimit: 100
        },
        
        // 文件上传配置
        upload: {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
        }
    },

    // 缓存配置
    cache: {
        ttl: 300, // 5分钟
        checkperiod: 600 // 10分钟检查一次过期
    },

    // 监控配置
    monitoring: {
        enabled: process.env.MONITORING_ENABLED === 'true',
        endpoint: process.env.MONITORING_ENDPOINT,
        interval: parseInt(process.env.MONITORING_INTERVAL) || 60000 // 1分钟
    }
};

/**
 * 验证配置
 */
function validateConfig() {
    const errors = [];

    // 验证必需的环境变量
    if (!config.email.imap.user) {
        errors.push('IMAP_USER 环境变量未设置');
    }

    if (!config.email.imap.password) {
        errors.push('IMAP_PASSWORD 环境变量未设置');
    }

    if (config.jwt.secret === 'your-super-secret-jwt-key-change-this-in-production') {
        console.warn('警告: 使用默认JWT密钥，生产环境请更改');
    }

    if (errors.length > 0) {
        console.error('配置验证失败:');
        errors.forEach(error => console.error(`- ${error}`));
        process.exit(1);
    }
}

/**
 * 获取配置
 * @param {string} path - 配置路径，如 'server.port'
 * @returns {any} 配置值
 */
function get(path) {
    return path.split('.').reduce((obj, key) => obj && obj[key], config);
}

/**
 * 设置配置
 * @param {string} path - 配置路径
 * @param {any} value - 配置值
 */
function set(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((obj, key) => {
        if (!obj[key]) obj[key] = {};
        return obj[key];
    }, config);
    target[lastKey] = value;
}

/**
 * 获取环境特定配置
 */
function getEnvironmentConfig() {
    const env = config.server.env;
    
    switch (env) {
        case 'development':
            return {
                ...config,
                logging: { ...config.logging, level: 'debug' },
                database: { ...config.database, options: { ...config.database.options, verbose: console.log } }
            };
            
        case 'production':
            return {
                ...config,
                logging: { ...config.logging, level: 'warn' },
                security: {
                    ...config.security,
                    helmet: {
                        ...config.security.helmet,
                        contentSecurityPolicy: {
                            ...config.security.helmet.contentSecurityPolicy,
                            directives: {
                                ...config.security.helmet.contentSecurityPolicy.directives,
                                scriptSrc: ["'self'"] // 生产环境更严格
                            }
                        }
                    }
                }
            };
            
        case 'test':
            return {
                ...config,
                database: { ...config.database, path: ':memory:' },
                email: { ...config.email, checkInterval: 1000 }, // 测试时更快的检查间隔
                logging: { ...config.logging, level: 'error' }
            };
            
        default:
            return config;
    }
}

// 导出配置和工具函数
module.exports = {
    ...getEnvironmentConfig(),
    validate: validateConfig,
    get,
    set,
    raw: config
};
