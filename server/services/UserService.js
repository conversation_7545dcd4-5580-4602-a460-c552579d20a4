const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

/**
 * 用户服务类
 * 处理用户认证、权限验证等功能
 */
class UserService {
    constructor() {
        this.allowedDomains = [process.env.DOMAIN || 'getmailapp.org'];
        this.jwtSecret = process.env.JWT_SECRET || 'default-secret-key';
        this.tokenExpiry = '24h';
    }

    /**
     * 验证邮箱地址格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmailFormat(email) {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(email);
    }

    /**
     * 验证邮箱域名是否被允许
     * @param {string} email - 邮箱地址
     * @returns {boolean} 域名是否被允许
     */
    validateEmailDomain(email) {
        const domain = email.split('@')[1];
        return this.allowedDomains.includes(domain);
    }

    /**
     * 验证用户名格式
     * @param {string} username - 用户名
     * @returns {boolean} 是否有效
     */
    validateUsername(username) {
        // 用户名只能包含字母、数字、下划线和连字符，长度3-20位
        const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
        return usernameRegex.test(username);
    }

    /**
     * 生成JWT令牌
     * @param {string} email - 用户邮箱
     * @returns {string} JWT令牌
     */
    generateToken(email) {
        const payload = {
            email: email,
            domain: email.split('@')[1],
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24小时后过期
        };

        return jwt.sign(payload, this.jwtSecret, { expiresIn: this.tokenExpiry });
    }

    /**
     * 验证JWT令牌
     * @param {string} token - JWT令牌
     * @returns {object|null} 解码后的用户信息或null
     */
    verifyToken(token) {
        try {
            const decoded = jwt.verify(token, this.jwtSecret);
            
            // 检查令牌是否过期
            if (decoded.exp < Math.floor(Date.now() / 1000)) {
                return null;
            }

            return decoded;
        } catch (error) {
            console.error('JWT验证失败:', error.message);
            return null;
        }
    }

    /**
     * 用户登录验证
     * @param {string} email - 邮箱地址
     * @returns {object} 登录结果
     */
    async authenticateUser(email) {
        try {
            // 验证邮箱格式
            if (!this.validateEmailFormat(email)) {
                return {
                    success: false,
                    error: '邮箱地址格式不正确'
                };
            }

            // 验证域名
            if (!this.validateEmailDomain(email)) {
                return {
                    success: false,
                    error: '邮箱域名不被支持'
                };
            }

            // 提取用户名并验证
            const username = email.split('@')[0];
            if (!this.validateUsername(username)) {
                return {
                    success: false,
                    error: '用户名格式不正确，只能包含字母、数字、下划线和连字符，长度3-20位'
                };
            }

            // 生成令牌
            const token = this.generateToken(email);

            return {
                success: true,
                token: token,
                email: email,
                username: username,
                message: '登录成功'
            };

        } catch (error) {
            console.error('用户认证错误:', error);
            return {
                success: false,
                error: '认证服务暂时不可用，请稍后重试'
            };
        }
    }

    /**
     * 验证用户权限
     * @param {string} token - JWT令牌
     * @param {string} requestedEmail - 请求访问的邮箱地址
     * @returns {object} 权限验证结果
     */
    async authorizeUser(token, requestedEmail = null) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '访问令牌无效或已过期'
                };
            }

            // 如果指定了请求的邮箱地址，检查是否匹配
            if (requestedEmail && decoded.email !== requestedEmail) {
                return {
                    success: false,
                    error: '无权访问该邮箱'
                };
            }

            return {
                success: true,
                user: {
                    email: decoded.email,
                    domain: decoded.domain,
                    username: decoded.email.split('@')[0]
                }
            };

        } catch (error) {
            console.error('用户授权错误:', error);
            return {
                success: false,
                error: '权限验证失败'
            };
        }
    }

    /**
     * 刷新令牌
     * @param {string} token - 当前令牌
     * @returns {object} 刷新结果
     */
    async refreshToken(token) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '令牌无效，请重新登录'
                };
            }

            // 检查令牌是否即将过期（剩余时间少于1小时）
            const timeUntilExpiry = decoded.exp - Math.floor(Date.now() / 1000);
            if (timeUntilExpiry > 3600) {
                return {
                    success: true,
                    token: token,
                    message: '令牌仍然有效'
                };
            }

            // 生成新令牌
            const newToken = this.generateToken(decoded.email);

            return {
                success: true,
                token: newToken,
                message: '令牌已刷新'
            };

        } catch (error) {
            console.error('令牌刷新错误:', error);
            return {
                success: false,
                error: '令牌刷新失败'
            };
        }
    }

    /**
     * 获取用户信息
     * @param {string} token - JWT令牌
     * @returns {object} 用户信息
     */
    async getUserInfo(token) {
        try {
            const authResult = await this.authorizeUser(token);
            
            if (!authResult.success) {
                return authResult;
            }

            return {
                success: true,
                user: authResult.user
            };

        } catch (error) {
            console.error('获取用户信息错误:', error);
            return {
                success: false,
                error: '获取用户信息失败'
            };
        }
    }

    /**
     * 注销用户（将令牌加入黑名单）
     * 注意：这是一个简化实现，生产环境中应该使用Redis等缓存来维护黑名单
     * @param {string} token - JWT令牌
     * @returns {object} 注销结果
     */
    async logoutUser(token) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '令牌无效'
                };
            }

            // 在实际应用中，这里应该将令牌加入黑名单
            // 目前只是简单返回成功
            
            return {
                success: true,
                message: '注销成功'
            };

        } catch (error) {
            console.error('用户注销错误:', error);
            return {
                success: false,
                error: '注销失败'
            };
        }
    }

    /**
     * 检查邮箱是否可用
     * @param {string} email - 邮箱地址
     * @returns {object} 检查结果
     */
    async checkEmailAvailability(email) {
        try {
            if (!this.validateEmailFormat(email)) {
                return {
                    success: false,
                    available: false,
                    error: '邮箱地址格式不正确'
                };
            }

            if (!this.validateEmailDomain(email)) {
                return {
                    success: false,
                    available: false,
                    error: '邮箱域名不被支持'
                };
            }

            const username = email.split('@')[0];
            if (!this.validateUsername(username)) {
                return {
                    success: false,
                    available: false,
                    error: '用户名格式不正确'
                };
            }

            return {
                success: true,
                available: true,
                message: '邮箱地址可用'
            };

        } catch (error) {
            console.error('检查邮箱可用性错误:', error);
            return {
                success: false,
                available: false,
                error: '检查失败'
            };
        }
    }
}

module.exports = UserService;
