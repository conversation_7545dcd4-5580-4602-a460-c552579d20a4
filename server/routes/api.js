const express = require('express');
const router = express.Router();
const UserService = require('../services/UserService');
const EmailService = require('../services/EmailService');

// 创建服务实例
const userService = new UserService();

/**
 * 中间件：验证JWT令牌
 */
const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({
            success: false,
            error: '访问令牌缺失'
        });
    }

    const authResult = await userService.authorizeUser(token);
    if (!authResult.success) {
        return res.status(401).json(authResult);
    }

    req.user = authResult.user;
    next();
};

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/auth/login', async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({
                success: false,
                error: '邮箱地址不能为空'
            });
        }

        const result = await userService.authenticateUser(email);
        res.json(result);

    } catch (error) {
        console.error('登录错误:', error);
        res.status(500).json({
            success: false,
            error: '服务器内部错误'
        });
    }
});

/**
 * 获取邮件列表
 * GET /api/emails
 */
router.get('/emails', authenticateToken, async (req, res) => {
    try {
        const { page = 1, limit = 20, search } = req.query;
        const offset = (parseInt(page) - 1) * parseInt(limit);
        const emailService = req.app.locals.emailService;

        let emails;
        let totalCount;

        if (search) {
            emails = await emailService.searchEmails(
                req.user.email,
                search,
                parseInt(limit),
                offset
            );
            // 搜索时的总数需要单独查询
            totalCount = emails.length; // 简化处理
        } else {
            emails = await emailService.getEmailsForUser(
                req.user.email,
                parseInt(limit),
                offset
            );
            totalCount = await emailService.getEmailCountForUser(req.user.email);
        }

        res.json({
            success: true,
            emails: emails,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / parseInt(limit))
            }
        });

    } catch (error) {
        console.error('获取邮件列表错误:', error);
        res.status(500).json({
            success: false,
            error: '服务器内部错误'
        });
    }
});

// 获取单个邮件详情
router.get('/emails/:id', authenticateToken, async (req, res) => {
    try {
        const { email } = req.user;
        const emailId = req.params.id;

        const emailService = new EmailService();
        const emailDetail = await emailService.getEmailById(emailId, email);

        if (!emailDetail) {
            return res.status(404).json({ error: '邮件不存在或无权访问' });
        }

        res.json({
            success: true,
            email: emailDetail
        });

    } catch (error) {
        console.error('获取邮件详情错误:', error);
        res.status(500).json({ error: '获取邮件详情失败，请稍后重试' });
    }
});

// 标记邮件为已读
router.put('/emails/:id/read', authenticateToken, async (req, res) => {
    try {
        const { email } = req.user;
        const emailId = req.params.id;

        const emailService = new EmailService();
        const result = await emailService.markEmailAsRead(emailId, email);

        if (!result) {
            return res.status(404).json({ error: '邮件不存在或无权访问' });
        }

        res.json({
            success: true,
            message: '邮件已标记为已读'
        });

    } catch (error) {
        console.error('标记邮件已读错误:', error);
        res.status(500).json({ error: '操作失败，请稍后重试' });
    }
});

// 获取用户统计信息
router.get('/stats', authenticateToken, async (req, res) => {
    try {
        const { email } = req.user;
        const emailService = new EmailService();
        
        const stats = await emailService.getUserStats(email);

        res.json({
            success: true,
            stats: stats
        });

    } catch (error) {
        console.error('获取统计信息错误:', error);
        res.status(500).json({ error: '获取统计信息失败，请稍后重试' });
    }
});

// 健康检查
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
