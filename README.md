# 临时邮箱管理系统

## 项目概述
这是一个专为账号销售业务设计的临时邮箱管理系统，完全模仿 getmailapp.org 的界面设计和用户体验。系统允许客户通过输入分配给他们的邮箱地址来自助查看邮件，提高业务效率。

## 核心功能
1. **用户独立访问** - 客户输入分配的邮箱地址即可查看该邮箱的邮件
2. **隐私隔离** - 每个客户只能看到分配给自己的邮箱邮件
3. **邮件过滤系统** - 根据收件人地址自动过滤和分发邮件
4. **响应式设计** - 支持移动端访问
5. **界面完全还原** - 百分百还原 getmailapp.org 的界面

## 技术架构
- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **后端**: Node.js + Express
- **数据库**: SQLite (轻量级，适合小规模部署)
- **邮件处理**: IMAP 客户端库
- **部署**: 可部署到任何支持 Node.js 的服务器

## 项目结构
```
/
├── public/                 # 静态资源
│   ├── css/               # 样式文件
│   ├── js/                # 前端JavaScript
│   └── images/            # 图片资源
├── views/                 # HTML模板
├── server/                # 后端代码
│   ├── routes/            # 路由处理
│   ├── models/            # 数据模型
│   └── services/          # 业务逻辑
├── config/                # 配置文件
└── database/              # 数据库文件
```

## 开发计划

### 第一阶段：基础架构搭建
1. 创建项目目录结构
2. 设置 Node.js 服务器
3. 配置数据库
4. 创建基础路由

### 第二阶段：界面开发
1. 分析 getmailapp.org 界面结构
2. 创建首页（未登录状态）
3. 创建登录界面
4. 创建邮箱管理界面
5. 实现响应式设计

### 第三阶段：核心功能实现
1. 用户认证系统
2. 邮件接收和解析
3. 邮件过滤和分发
4. 邮件列表显示
5. 邮件详情查看

### 第四阶段：优化和测试
1. 性能优化
2. 安全性加固
3. 功能测试
4. 界面细节调整

## 使用说明

### 管理员操作
1. 为客户分配临时邮箱地址（格式：<EMAIL>）
2. 配置主邮箱接收所有邮件
3. 系统自动根据收件人地址过滤邮件

### 客户操作
1. 访问系统网站
2. 输入分配的邮箱地址
3. 查看该邮箱收到的所有邮件
4. 获取验证码等信息

## 安全特性
- 邮箱地址验证
- 隐私隔离保护
- 防止跨用户访问
- 安全的邮件内容显示

## 部署要求
- Node.js 14+ 
- 支持 IMAP 的邮箱服务
- 域名和 SSL 证书（推荐）

## 开发状态
- [x] 项目初始化和基础架构搭建
- [x] 界面分析和设计实现
- [x] 用户认证系统开发
- [x] 邮件处理核心功能
- [x] 前端交互功能实现
- [x] 系统测试和优化

## 已完成功能

### 1. 完整的界面还原
- ✅ 完全模仿 getmailapp.org 的界面设计
- ✅ 响应式设计，支持移动端
- ✅ 暗色/亮色主题切换
- ✅ 现代化的用户界面和交互体验

### 2. 用户认证系统
- ✅ 基于邮箱地址的用户认证
- ✅ JWT令牌管理
- ✅ 隐私隔离保护
- ✅ 安全的权限验证

### 3. 邮件管理功能
- ✅ 自动邮件接收和解析
- ✅ 邮件列表显示和分页
- ✅ 邮件详情查看
- ✅ 邮件搜索功能
- ✅ 已读/未读状态管理

### 4. 系统特性
- ✅ 实时邮件检查
- ✅ 自动过期邮件清理
- ✅ 数据库优化和索引
- ✅ 错误处理和日志记录
- ✅ 安全防护措施

## 技术实现细节

### 前端技术栈
- **HTML5 + CSS3**: 现代化的界面设计
- **原生JavaScript**: 高性能的前端交互
- **CSS变量**: 支持主题切换
- **响应式设计**: 完美适配各种设备

### 后端技术栈
- **Node.js + Express**: 高性能的服务器框架
- **SQLite**: 轻量级数据库，适合小规模部署
- **IMAP客户端**: 自动邮件接收
- **JWT认证**: 安全的用户认证
- **邮件解析**: 支持HTML和纯文本邮件

### 安全特性
- **JWT令牌认证**: 安全的用户会话管理
- **权限隔离**: 用户只能访问自己的邮件
- **速率限制**: 防止API滥用
- **输入验证**: 防止XSS和注入攻击
- **HTTPS支持**: 数据传输加密

## 快速开始

### 1. 环境要求
- Node.js 14+
- 支持IMAP的邮箱服务
- 域名（可选，用于生产环境）

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
复制 `.env` 文件并修改配置：
```bash
# 邮件服务器配置
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-app-password
IMAP_TLS=true

# 域名配置
DOMAIN=getmailapp.org

# JWT密钥（生产环境请更改）
JWT_SECRET=your-super-secret-jwt-key
```

### 4. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 5. 访问系统
打开浏览器访问 `http://localhost:3000`

## 部署指南

### 1. 服务器要求
- Linux/Windows/macOS
- Node.js 14+
- 至少 512MB RAM
- 1GB 磁盘空间

### 2. 生产环境配置
```bash
# 设置环境变量
export NODE_ENV=production
export PORT=3000
export DOMAIN=yourdomain.com

# 启动服务
npm start
```

### 3. 使用PM2部署（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server.js --name "temp-email"

# 设置开机自启
pm2 startup
pm2 save
```

### 4. Nginx反向代理配置
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```
